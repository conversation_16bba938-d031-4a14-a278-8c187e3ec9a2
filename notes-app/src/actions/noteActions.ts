"user server";
import { eq, not } from "drizzle-orm";
import { revalidatePath } from "next/cache";
import { db } from "@/db/drizzle";
import { notes, users } from "@/db/schema";

export const getData = async () => {
    const data = await db.select().from(notes);
    return data;
}

export const deleteData = async (id: number) => {
    await db
        .update(notes)
        .where(eq(notes.id, id);
               
    revalidatePath("/");
};

export const editNote = async (id: number , content: string) => {
    await db
        .update(notes)
        .set({
            text: text
        })
        .where(eq(notes.id, id);
    revalidatePath("/");
