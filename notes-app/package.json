{"name": "notes-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio"}, "dependencies": {"@clerk/nextjs": "^6.28.0", "@neondatabase/serverless": "^1.0.1", "drizzle-orm": "^0.44.4", "next": "15.4.5", "pg": "^8.16.3", "react": "19.1.0", "react-dom": "19.1.0", "svix": "^1.70.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.31.4", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4", "typescript": "^5"}}