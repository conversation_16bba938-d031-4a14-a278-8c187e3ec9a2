{"name": "json-share-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts"}, "dependencies": {"@clerk/nextjs": "^6.28.0", "@prisma/client": "^6.1.0", "next": "15.4.5", "react": "19.1.0", "react-dom": "19.1.0", "zod": "^3.22.4", "nanoid": "^5.0.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "prisma": "^6.1.0", "tailwindcss": "^4", "tsx": "^4.7.0", "typescript": "^5"}}