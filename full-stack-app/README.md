<!--
README Documentation

This README provides an overview and setup instructions for a fullstack CRUD application featuring authentication. The stack includes Next.js, TypeScript, MongoDB, Tailwind CSS, and NextAuth.js. Key features are secure API routes, session validation, and a React-based frontend with form validation.

Sections include:
- Project Overview and MVP features
- Completed backend and authentication setup
- To-Do list for frontend CRUD UI, user dashboard, authentication UI, and deployment steps
- Step-by-step instructions for running the project locally

Badges indicate technology versions used. The README is structured to guide contributors through development and deployment, highlighting both finished and pending tasks.
-->

# 🔥 Fullstack CRUD App with Authentication

[![Next.js](https://img.shields.io/badge/Next.js-13.4-blue?logo=next.js)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-4.9-blue?logo=typescript)](https://www.typescriptlang.org/)
[![MongoDB](https://img.shields.io/badge/MongoDB-6.0-green?logo=mongodb)](https://www.mongodb.com/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-3.2-blue?logo=tailwind-css)](https://tailwindcss.com/)
[![NextAuth](https://img.shields.io/badge/NextAuth.js-4.20-blueviolet)](https://next-auth.js.org/)

---

## Project Overview

A fullstack CRUD application with user authentication using Next.js, TypeScript, MongoDB, and NextAuth.js. Features include secure API routes, session validation, and a React-based frontend with form validation.

---

## ✅ Completed

- MongoDB configuration
- NextAuth authentication setup
- Auth-protected `/api/posts` route
- Session validation in API route handlers
- Mongoose data models

---

## 🔜 To Do

### 1. Frontend CRUD UI
- ✅ Protected API routes
- 🔲 Display posts (SSR or client fetch)
- 🔲 Add Post form (React Hook Form + Zod validation)
- 🔲 Delete post button
- 🔲 Edit post button (optional)

### 2. User Dashboard
- 🔲 `/dashboard` page:
    - Welcome message (`session.user.name`)
    - User’s posts (optional)
    - Sign out button

### 3. Authentication UI
- 🔲 Login page (`/login`)
- 🔲 Sign Up (optional, or GitHub OAuth only)
- 🔲 Login/Logout toggle button

### 4. Deployment to Vercel
- 🔲 Add environment variables to Vercel:
    - `MONGODB_URI`
    - `NEXTAUTH_SECRET`
    - `GITHUB_CLIENT_ID` & `GITHUB_CLIENT_SECRET`
- 🔲 Push to GitHub and deploy
- 🔲 Test live app

---

## 💥 Minimum Viable Product (MVP)

- Post model implemented
- `/api/posts` protected with authentication
- Page to display posts
- Add post form with validation
- Login button using NextAuth
- Session management working
- Hosted on Vercel

---

## How to Run

1. Clone the repository:
     ```bash
     git clone <repo-url>
     cd full-stack-app
     ```
2. Install dependencies:
     ```bash
     npm install
     ```
3. Create a `.env.local` file and add required environment variables:
     ```
     MONGODB_URI=your_mongodb_uri
     NEXTAUTH_SECRET=your_nextauth_secret
     GITHUB_CLIENT_ID=your_github_client_id
     GITHUB_CLIENT_SECRET=your_github_client_secret
     ```
4. Run the development server:
     ```bash
     npm run dev
     ```
5. Open [http://localhost:3000](http://localhost:3000) in your browser.

---

## Contributing

Feel free to open issues or submit pull requests for improvements and new features.

---

## License

MIT


