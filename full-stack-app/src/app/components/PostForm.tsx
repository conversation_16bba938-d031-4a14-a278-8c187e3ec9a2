"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { postSchema, PostSchemaType } from "@/schemas/postSchema";

export default function PostForm() {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<PostSchemaType>({
    resolver: zodResolver(postSchema),
  });

  const onSubmit = async (data: PostSchemaType) => {
    const res = await fetch("/api/posts", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(data),
    });

    if (res.ok) {
      reset();
      alert("✅ Post created!");
    } else {
      alert("❌ Failed to create post");
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 p-4">
      <div>
        <label className="block">Title</label>
        <input {...register("title")} className="border p-2 w-full" />
        {errors.title && <p className="text-red-500">{errors.title.message}</p>}
      </div>

      <div>
        <label className="block">Content</label>
        <textarea {...register("content")} className="border p-2 w-full" />
        {errors.content && <p className="text-red-500">{errors.content.message}</p>}
      </div>

      <div>
        <label className="block">Author</label>
        <input {...register("author")} className="border p-2 w-full" />
        {errors.author && <p className="text-red-500">{errors.author.message}</p>}
      </div>

      <button
        type="submit"
        disabled={isSubmitting}
        className="bg-blue-500 text-white px-4 py-2 rounded"
      >
        {isSubmitting ? "Submitting..." : "Create Post"}
      </button>
    </form>
  );
}


