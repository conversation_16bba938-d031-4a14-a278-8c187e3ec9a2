import NextAuth from "next-auth";
import Gith<PERSON><PERSON><PERSON>ider from "next-auth/providers/github";
import { SessionStrategy } from "next-auth";

export const authOptions = {
    providers: [
        GithubProvider({
            clientId: process.env.GITHUB_ID!,
            clientSecret: process.env.GITHUB_SECRET!,
        }),
    ],
    secret: process.env.NEXTAUTH_SECRET,
    callbacks: {
        async signIn({user, account, profile}){
            const exisitingUser = await User.findOne({email: user.email});
            if(!exisitingUser){
                await User.create({email: user.email, name: user.name, image: user.image});
            }
            return true;
        },

        async jwt({token, user}){
            if(user){
                token.id = user.id;
            }
            return token;
        },

        async session({session, token}){
            if(token?.id) session.user.id = token.id;
            return session;
        },
    },
    session: {
        strategy: "jwt" as SessionStrategy,
    },  
};

const handler = NextAuth(authOptions);

export { handler as GET , handler as POST };


