// src/app/posts/page.tsx
import { Post } from "@/models/Post";
import { connectDB } from "@/lib/mongoose";

export const dynamic = "force-dynamic"; // So it always runs on server

async function getPosts() {
  await connectDB();
  const posts = await Post.find().lean(); // faster, returns plain JS objects
  return posts;
}

export default async function PostsPage() {
  const posts = await getPosts();

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">All Posts</h1>
      <ul className="space-y-4">
        {posts.map((post: any) => (
          <li
            key={post._id}
            className="border p-4 rounded shadow-sm bg-white"
          >
            <h2 className="text-xl font-semibold">{post.title}</h2>
            <p className="text-gray-700">{post.content}</p>
          </li>
        ))}
      </ul>
    </div>
  );
}

