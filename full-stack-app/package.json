{"name": "full-stack-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@types/next-auth": "^3.13.0", "autoprefixer": "^10.4.21", "mongoose": "^8.17.0", "next": "15.4.5", "next-auth": "^4.24.11", "postcss": "^8.5.6", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "zod": "^4.0.14"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4.1.11", "typescript": "^5"}}